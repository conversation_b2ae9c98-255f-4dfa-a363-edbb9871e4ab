# LLaMA Integration Requirements for FS Assistant
# Install these packages to enable real LLaMA model integration

# Core LLaMA requirements
transformers>=4.35.0
torch>=2.1.0
accelerate>=0.24.0
bitsandbytes>=0.41.0  # For 4-bit quantization
sentencepiece>=0.1.99

# Hugging Face integration
datasets>=2.14.0
tokenizers>=0.14.0
huggingface-hub>=0.17.0

# GPU acceleration
# For CUDA 11.8: torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
# For CUDA 12.1: torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121

# Memory optimization
psutil>=5.9.0
safetensors>=0.4.0

# FastAPI and async support
fastapi>=0.104.0
uvicorn>=0.24.0
pydantic>=2.4.0

# Environment configuration
python-dotenv>=1.0.0

# Optional: For even better performance
# flash-attn>=2.3.0  # Requires specific CUDA setup
# xformers>=0.0.22  # Memory efficient attention

# Installation Instructions:
# 
# 1. Basic AI setup:
#    pip install -r requirements-ai.txt
#
# 2. For LLaMA-2 integration:
#    - Get access to meta-llama models on Hugging Face
#    - Set HF_TOKEN environment variable
#    - Ensure you have sufficient GPU memory (13B model needs ~26GB VRAM)
#
# 3. For OpenAI integration:
#    - Get API key from OpenAI
#    - Set OPENAI_API_KEY environment variable
#
# 4. For Claude integration:
#    - Get API key from Anthropic
#    - Set ANTHROPIC_API_KEY environment variable
#
# 5. Environment variables (.env file):
#    OPENAI_API_KEY=your_openai_key_here
#    ANTHROPIC_API_KEY=your_claude_key_here
#    HUGGINGFACE_TOKEN=your_hf_token_here
#    AI_MODEL_TYPE=openai  # or llama2, claude, huggingface, local
#
# 6. GPU Requirements:
#    - LLaMA-2 7B: 14GB+ VRAM
#    - LLaMA-2 13B: 26GB+ VRAM
#    - LLaMA-2 70B: 140GB+ VRAM (multi-GPU setup)
#
# 7. CPU-only setup:
#    - Use smaller models or quantized versions
#    - Consider using llama-cpp-python for CPU inference
#    - API-based models (OpenAI, Claude) work on any hardware
