#!/usr/bin/env python3
"""
Setup script for LLaMA integration with FS Assistant
Automatically installs required packages and configures the environment
"""

import subprocess
import sys
import os
import platform
import torch

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed: {e}")
        print(f"Error output: {e.stderr}")
        return False

def check_gpu():
    """Check GPU availability and CUDA version"""
    print("🔍 Checking GPU and CUDA availability...")
    
    if torch.cuda.is_available():
        gpu_count = torch.cuda.device_count()
        gpu_name = torch.cuda.get_device_name(0)
        cuda_version = torch.version.cuda
        print(f"✅ GPU detected: {gpu_name}")
        print(f"✅ CUDA version: {cuda_version}")
        print(f"✅ Available GPUs: {gpu_count}")
        return True
    else:
        print("⚠️ No GPU detected - will use CPU (slower)")
        return False

def install_pytorch():
    """Install PyTorch with appropriate CUDA support"""
    print("🔄 Installing PyTorch...")
    
    # Detect CUDA version
    has_gpu = check_gpu()
    
    if has_gpu:
        # Install CUDA version
        cuda_commands = [
            "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu121",
            "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118"
        ]
        
        for cmd in cuda_commands:
            if run_command(cmd, "Installing PyTorch with CUDA"):
                return True
    
    # Fallback to CPU version
    return run_command("pip install torch torchvision torchaudio", "Installing PyTorch (CPU)")

def install_transformers():
    """Install transformers and related packages"""
    packages = [
        "transformers>=4.35.0",
        "accelerate>=0.24.0",
        "bitsandbytes>=0.41.0",
        "sentencepiece>=0.1.99",
        "tokenizers>=0.14.0",
        "huggingface-hub>=0.17.0",
        "safetensors>=0.4.0",
        "psutil>=5.9.0"
    ]
    
    for package in packages:
        if not run_command(f"pip install {package}", f"Installing {package}"):
            return False
    
    return True

def setup_huggingface():
    """Setup Hugging Face authentication"""
    print("🔄 Setting up Hugging Face access...")
    
    try:
        from huggingface_hub import login
        
        print("📝 To access LLaMA models, you need a Hugging Face token.")
        print("1. Go to https://huggingface.co/settings/tokens")
        print("2. Create a new token with 'Read' permissions")
        print("3. Request access to meta-llama models:")
        print("   - https://huggingface.co/meta-llama/Llama-3-8b-chat-hf")
        print("   - https://huggingface.co/meta-llama/Llama-2-13b-chat-hf")
        
        token = input("\n🔑 Enter your Hugging Face token (or press Enter to skip): ").strip()
        
        if token:
            login(token=token)
            print("✅ Hugging Face authentication successful!")
            return True
        else:
            print("⚠️ Skipped Hugging Face authentication - you'll need to set this up later")
            return False
            
    except Exception as e:
        print(f"❌ Hugging Face setup failed: {e}")
        return False

def test_llama_access():
    """Test if we can access LLaMA models"""
    print("🔄 Testing LLaMA model access...")
    
    try:
        from transformers import AutoTokenizer
        
        models_to_test = [
            "meta-llama/Llama-3-8b-chat-hf",
            "meta-llama/Llama-2-13b-chat-hf",
            "meta-llama/Llama-2-7b-chat-hf"
        ]
        
        for model_name in models_to_test:
            try:
                print(f"🔍 Testing access to {model_name}...")
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                print(f"✅ Access confirmed for {model_name}")
                return True
            except Exception as e:
                print(f"❌ Cannot access {model_name}: {e}")
                continue
        
        print("❌ No LLaMA models accessible - check your Hugging Face permissions")
        return False
        
    except Exception as e:
        print(f"❌ Error testing model access: {e}")
        return False

def create_env_file():
    """Create environment configuration file"""
    print("🔄 Creating environment configuration...")
    
    env_content = """# FS Assistant LLaMA Configuration
# Model selection (in order of preference)
LLAMA_MODEL_PRIMARY=meta-llama/Llama-3-8b-chat-hf
LLAMA_MODEL_FALLBACK=meta-llama/Llama-2-13b-chat-hf
LLAMA_MODEL_MINIMAL=meta-llama/Llama-2-7b-chat-hf

# Performance settings
USE_4BIT_QUANTIZATION=true
MAX_NEW_TOKENS=512
TEMPERATURE=0.7
TOP_P=0.9

# Memory management
MAX_CONVERSATION_HISTORY=20
TOKEN_LIMIT=4000

# Hugging Face settings
# HF_TOKEN=your_token_here  # Uncomment and add your token
"""
    
    try:
        with open("backend/.env", "w") as f:
            f.write(env_content)
        print("✅ Environment file created at backend/.env")
        return True
    except Exception as e:
        print(f"❌ Failed to create environment file: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 FS Assistant LLaMA Setup")
    print("=" * 50)
    
    # Check Python version
    python_version = sys.version_info
    if python_version < (3, 8):
        print("❌ Python 3.8+ required")
        return False
    
    print(f"✅ Python {python_version.major}.{python_version.minor} detected")
    
    # Install packages
    steps = [
        (install_pytorch, "Installing PyTorch"),
        (install_transformers, "Installing Transformers"),
        (setup_huggingface, "Setting up Hugging Face"),
        (test_llama_access, "Testing LLaMA access"),
        (create_env_file, "Creating configuration")
    ]
    
    for step_func, step_name in steps:
        print(f"\n📋 Step: {step_name}")
        if not step_func():
            print(f"⚠️ {step_name} had issues - continuing anyway")
    
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("1. Start the LLaMA-powered backend:")
    print("   cd backend && python -m app.main_llama")
    print("\n2. Test with these questions:")
    print("   - 'What is AI?'")
    print("   - 'How to cook pasta?'")
    print("   - 'ما هي خدمات سوفليكس؟'")
    print("   - 'Tell me a joke'")
    
    print("\n💡 Troubleshooting:")
    print("- If models don't load: Check GPU memory (8GB+ recommended)")
    print("- If access denied: Ensure Hugging Face token has LLaMA permissions")
    print("- If slow: Models will download on first use (several GB)")
    
    return True

if __name__ == "__main__":
    main()
