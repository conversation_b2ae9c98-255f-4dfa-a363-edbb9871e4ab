#!/usr/bin/env python3
"""
Test script for the enhanced FS chatbot functionality
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.app.main_llama import (
    normalize_input, 
    categorize_question, 
    get_dynamic_fallback_response,
    update_conversation_context,
    get_contextual_response_enhancement
)

def test_input_normalization():
    """Test the input normalization functionality"""
    print("🧪 Testing Input Normalization:")
    print("-" * 50)
    
    test_cases = [
        "hii there!",
        "wat do u do?",
        "pls help me with my app",
        "i need 2 build a website 4 my business",
        "softlex is great!",
        "can u help me???",
    ]
    
    for test_input in test_cases:
        normalized = normalize_input(test_input)
        print(f"Input:  '{test_input}'")
        print(f"Output: '{normalized}'")
        print()

def test_question_categorization():
    """Test the enhanced question categorization"""
    print("🎯 Testing Question Categorization:")
    print("-" * 50)
    
    test_cases = [
        ("hii", "greeting"),
        ("hello there", "greeting"),
        ("مرحبا", "greeting"),
        ("what does sofflex do?", "company"),
        ("i need help building an app", "project"),
        ("how to use react?", "technical"),
        ("help me with ui design", "design"),
        ("how to make coffee?", "general"),
        ("what is the weather like?", "general"),
    ]
    
    for test_input, expected in test_cases:
        category = categorize_question(test_input)
        status = "✅" if category == expected else "❌"
        print(f"{status} '{test_input}' -> {category} (expected: {expected})")

def test_creative_responses():
    """Test creative response generation"""
    print("🎨 Testing Creative Response Generation:")
    print("-" * 50)
    
    test_cases = [
        "hii",
        "what does flexible soft do?",
        "how to make coffee?",
        "i love traveling",
        "help me with design",
        "مرحبا كيف الحال؟",
    ]
    
    session_id = "test-session"
    
    for test_input in test_cases:
        print(f"Input: '{test_input}'")
        response = get_dynamic_fallback_response(test_input, session_id)
        print(f"Response: {response[:200]}...")
        print("-" * 30)

def test_context_tracking():
    """Test conversation context tracking"""
    print("🧠 Testing Context Tracking:")
    print("-" * 50)
    
    session_id = "context-test-session"
    
    # Simulate a conversation
    conversation = [
        "hi there",
        "i'm interested in building a mobile app",
        "what technologies do you recommend?",
        "i also need help with design",
        "how much would it cost?",
    ]
    
    for i, message in enumerate(conversation):
        print(f"Message {i+1}: '{message}'")
        
        # Update context
        category = categorize_question(message)
        update_conversation_context(session_id, message, category)
        
        # Get response
        response = get_dynamic_fallback_response(message, session_id)
        print(f"Response: {response[:150]}...")
        print()

if __name__ == "__main__":
    print("🚀 Enhanced FS Chatbot Test Suite")
    print("=" * 60)
    print()
    
    test_input_normalization()
    print()
    
    test_question_categorization()
    print()
    
    test_creative_responses()
    print()
    
    test_context_tracking()
    print()
    
    print("✅ All tests completed!")
