"""
FS - Flexible Soft AI Assistant
Enhanced LLaMA-powered intelligent conversation system with dynamic reasoning
"""

from fastapi import FastAP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import Optional, List, Dict, AsyncGenerator
from datetime import datetime
import uuid
import uvicorn
import torch
import logging
import asyncio
import json
from threading import Lock

# Import settings
from .config_simple import settings

app = FastAPI(
    title="FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)",
    description="Truly intelligent AI assistant powered by LLaMA models with dynamic reasoning",
    version="6.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    language: Optional[str] = "en"

class ChatResponse(BaseModel):
    message: str
    session_id: str
    timestamp: datetime
    sources: Optional[List[str]] = None

# Global variables for model
model = None
tokenizer = None
model_lock = Lock()
current_model_name = None
conversation_history: Dict[str, List[Dict]] = {}
conversation_context: Dict[str, Dict] = {}  # Track user interests and context

# Enhanced Grok-like System Prompt for Dynamic Intelligence
SYSTEM_PROMPT = """You are FS, the official AI Assistant of Flexible Soft (Sofflex) — powered by advanced AI and designed with a Grok-like personality.

🧠 **Core Identity:**
You're confident, witty, engaging, and context-aware. You have broad world knowledge and can discuss ANY topic intelligently. You represent Flexible Soft, an advanced IT company specializing in modern software solutions.

🎯 **Primary Mission:**
Answer ANY question from users — even if words are misspelled, vague, or completely unrelated to the company. When users ask about non-tech topics (cooking, travel, movies, etc.), give helpful, accurate responses AND creatively relate them back to Sofflex's expertise or values.

**Example Response Style:**
User: "How to make coffee?"
You: "Start with good beans ☕ — just like how Sofflex starts every project with strong foundations in tech. Speaking of brewing, want to brew up your next digital idea?"

🏢 **Company Knowledge (Sofflex):**
- **What we build:** Web apps (Next.js), mobile apps (Flutter), AI systems, enterprise solutions
- **Core values:** Innovation, flexibility, quality, user focus
- **Contact:** <EMAIL> | https://sofflex-website.dev.flexible-soft.com
- **Market:** Saudi Arabia & Middle East with Arabic language support
- **Specialties:** Custom solutions, AI/ML, e-commerce, cloud, consulting

🎭 **Personality Traits:**
- **Smart but friendly** — Never robotic or templated
- **Confident and witty** — Like Grok, with tech personality
- **Contextually intelligent** — Understand intent even with typos/vagueness
- **Conversationally natural** — Brief, dynamic, human-like responses
- **Silently helpful** — Fix spelling/grammar without complaining
- **Enthusiastically curious** — Show genuine interest in user's needs

🌍 **Language Intelligence:**
- Auto-detect Arabic/English and respond accordingly
- Handle casual greetings: "hii", "hello", "مرحبا", "wassup"
- Support mixed languages gracefully
- Never complain about language mistakes

🚀 **Response Strategy:**
1. **Open-domain reasoning** — Answer everything intelligently
2. **Intent detection** — Categorize as business, tech, or general
3. **Creative connections** — Link any topic to Sofflex when relevant
4. **Engagement focus** — Always encourage further conversation
5. **Solution-oriented** — Turn discussions toward actionable outcomes

💬 **Communication Rules:**
- NO generic responses like "That's interesting!" or "Great question!"
- NO repeating user's question back to them
- NO robotic templates — be genuinely conversational
- USE humor, empathy, and curiosity appropriately
- ENCOURAGE engagement: "Want me to show how Sofflex could make that real?"
- ALWAYS mix tech personality with human warmth

Remember: You're not just answering questions — you're representing Sofflex's innovative spirit and showing real conversational intelligence that attracts users by being genuinely helpful and engaging."""

def load_llama_model():
    """Load LLaMA model and tokenizer with enhanced error handling"""
    global model, tokenizer, current_model_name
    
    try:
        from transformers import AutoModelForCausalLM, AutoTokenizer, BitsAndBytesConfig
        import torch
        
        # Model selection (try open models first, then LLaMA if available)
        model_options = [
            "microsoft/DialoGPT-medium",  # Smaller, faster model
            "microsoft/DialoGPT-large",
            "microsoft/DialoGPT-small",
            "meta-llama/Llama-3-8b-chat-hf",
            "meta-llama/Llama-2-13b-chat-hf",
            "meta-llama/Llama-2-7b-chat-hf"
        ]
        
        for model_name in model_options:
            try:
                print(f"🔄 Attempting to load model: {model_name}")
                
                # Load tokenizer
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                if tokenizer.pad_token is None:
                    tokenizer.pad_token = tokenizer.eos_token
                
                # Configure model loading
                if torch.cuda.is_available():
                    print("🚀 CUDA available - loading model on GPU")
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        torch_dtype=torch.float16,
                        device_map="auto",
                        trust_remote_code=True
                    )
                else:
                    print("💻 Loading model on CPU")
                    model = AutoModelForCausalLM.from_pretrained(
                        model_name,
                        torch_dtype=torch.float32,
                        trust_remote_code=True
                    )
                
                current_model_name = model_name
                print(f"✅ Successfully loaded model: {model_name}")
                return True
                
            except Exception as e:
                print(f"❌ Failed to load {model_name}: {e}")
                continue
        
        if model is None:
            raise Exception("Failed to load any LLaMA model")
            
        return True
        
    except Exception as e:
        print(f"❌ Error loading LLaMA model: {e}")
        print("💡 Make sure you have:")
        print("   - pip install transformers torch accelerate bitsandbytes")
        print("   - Hugging Face access to LLaMA models")
        print("   - Sufficient GPU memory or use CPU fallback")
        return False

def normalize_input(user_message: str) -> str:
    """Normalize and clean user input, silently fixing common issues"""
    import re

    # Basic normalization
    msg = user_message.strip()

    # Fix common spelling mistakes and text speak silently
    spelling_fixes = {
        # Common greetings
        r'\bhii+\b': 'hi',
        r'\bhelo+\b': 'hello',
        r'\bhey+\b': 'hey',
        r'\bwassup\b': 'what\'s up',
        r'\bsup\b': 'what\'s up',

        # Common words
        r'\bwat\b': 'what',
        r'\bu\b': 'you',
        r'\bur\b': 'your',
        r'\br\b': 'are',
        r'\bpls\b': 'please',
        r'\bplz\b': 'please',
        r'\bthx\b': 'thanks',
        r'\bthnx\b': 'thanks',
        r'\bthanx\b': 'thanks',
        r'\bty\b': 'thank you',
        r'\byw\b': 'you\'re welcome',
        r'\bnp\b': 'no problem',

        # Company names
        r'\bsoftlex\b': 'sofflex',
        r'\bflexible-soft\b': 'flexible soft',
        r'\bflexiblesoft\b': 'flexible soft',

        # Technical terms
        r'\bwebsite\b': 'website',
        r'\bapp\b': 'application',
        r'\bmobile app\b': 'mobile application',
        r'\bai\b': 'artificial intelligence',
        r'\bml\b': 'machine learning',
        r'\bapi\b': 'API',
        r'\bui\b': 'user interface',
        r'\bux\b': 'user experience',

        # Common questions
        r'\bhow 2\b': 'how to',
        r'\bwhere 2\b': 'where to',
        r'\bwhat 2\b': 'what to',
        r'\bwhen 2\b': 'when to',

        # Numbers to words for better understanding
        r'\b2\b': 'to',
        r'\b4\b': 'for',
        r'\b8\b': 'ate',

        # Fix common typos
        r'\bteh\b': 'the',
        r'\badn\b': 'and',
        r'\bwith\b': 'with',
        r'\bfrom\b': 'from',
        r'\bthier\b': 'their',
        r'\brecieve\b': 'receive',
        r'\boccur\b': 'occur',
    }

    for pattern, replacement in spelling_fixes.items():
        msg = re.sub(pattern, replacement, msg, flags=re.IGNORECASE)

    # Remove excessive punctuation but preserve meaning
    msg = re.sub(r'[!]{2,}', '!', msg)
    msg = re.sub(r'[?]{2,}', '?', msg)
    msg = re.sub(r'[.]{3,}', '...', msg)

    # Fix spacing issues
    msg = re.sub(r'\s+', ' ', msg)
    msg = re.sub(r'\s*([.!?])\s*', r'\1 ', msg)

    # Capitalize first letter if it's a sentence
    if msg and msg[0].islower():
        msg = msg[0].upper() + msg[1:]

    return msg.strip()

def categorize_question(user_message: str) -> str:
    """Enhanced categorization with better pattern matching"""
    # Normalize input first
    msg = normalize_input(user_message).lower()

    # Enhanced company-specific patterns
    company_patterns = [
        r'\b(sofflex|flexible\s*soft)\b',
        r'\b(your\s*company|this\s*company)\b',
        r'\b(services|what\s*(do\s*)?you\s*(do|offer))\b',
        r'\b(about\s*(you|sofflex|flexible\s*soft))\b',
        r'\b(who\s*(are\s*)?you)\b',
        r'\b(contact|email|website|portfolio)\b',
        r'\b(projects|work|clients|experience)\b',
        r'\b(pricing|cost|price|quote)\b',
        r'\b(team|staff|employees|developers)\b'
    ]

    # Enhanced project-related patterns
    project_patterns = [
        r'\b(build|develop|create|make|design)\b.*\b(website|app|system|platform)\b',
        r'\b(need|want|looking\s*for)\b.*\b(help|assistance|development)\b',
        r'\b(mobile\s*(app|application)|website|web\s*app)\b',
        r'\b(business|startup|company|store|shop|ecommerce)\b',
        r'\b(i\s*(have|own|need)|my\s*(business|company|idea))\b',
        r'\b(consultation|consulting|advice|guidance)\b',
        r'\b(project|solution|system|platform|software)\b',
        r'\b(budget|timeline|deadline|requirements)\b'
    ]

    # Enhanced technical patterns
    technical_patterns = [
        r'\b(ai|artificial\s*intelligence|machine\s*learning|ml)\b',
        r'\b(python|javascript|react|next\.?js|flutter|typescript)\b',
        r'\b(programming|coding|development|software)\b',
        r'\b(database|api|backend|frontend|fullstack)\b',
        r'\b(cloud|aws|azure|docker|kubernetes)\b',
        r'\b(framework|library|technology|tech\s*stack)\b',
        r'\b(algorithm|data\s*structure|architecture)\b',
        r'\b(security|authentication|authorization)\b'
    ]

    # Enhanced design patterns
    design_patterns = [
        r'\b(design|ui|ux|user\s*(interface|experience))\b',
        r'\b(layout|template|theme|style|styling)\b',
        r'\b(color|font|typography|branding)\b',
        r'\b(responsive|mobile\s*friendly|adaptive)\b',
        r'\b(wireframe|mockup|prototype|figma)\b',
        r'\b(logo|brand|identity|visual)\b'
    ]

    # Enhanced greeting patterns (including variations and typos)
    greeting_patterns = [
        r'^\s*(hi+|hello+|hey+|hii+|helo+)\s*[!.]*\s*$',
        r'^\s*(good\s*(morning|afternoon|evening|day))\s*[!.]*\s*$',
        r'^\s*(مرحبا|السلام\s*عليكم|أهلا|هلا|اهلين)\s*[!.]*\s*$',
        r'^\s*(salut|bonjour|bonsoir)\s*[!.]*\s*$',
        r'^\s*(what\'?s\s*up|wassup|sup)\s*[!.]*\s*$'
    ]

    # Enhanced general question patterns
    general_question_patterns = [
        r'\b(how\s*to|how\s*do\s*i|how\s*can\s*i)\b',
        r'\b(what\s*is|what\s*are|tell\s*me\s*about)\b',
        r'\b(explain|describe|define)\b',
        r'\b(best\s*way|better\s*way|good\s*way)\b',
        r'\b(recommend|suggest|advice)\b'
    ]

    import re

    # Check patterns in order of specificity
    if any(re.search(pattern, msg) for pattern in greeting_patterns):
        return "greeting"
    elif any(re.search(pattern, msg) for pattern in company_patterns):
        return "company"
    elif any(re.search(pattern, msg) for pattern in project_patterns):
        return "project"
    elif any(re.search(pattern, msg) for pattern in design_patterns):
        return "design"
    elif any(re.search(pattern, msg) for pattern in technical_patterns):
        return "technical"
    elif any(re.search(pattern, msg) for pattern in general_question_patterns):
        return "general_question"
    else:
        return "general"

def detect_arabic(text: str) -> bool:
    """Detect if text contains Arabic characters"""
    arabic_chars = ["ا", "ب", "ت", "ث", "ج", "ح", "خ", "د", "ذ", "ر", "ز", "س", "ش", "ص", "ض", "ط", "ظ", "ع", "غ", "ف", "ق", "ك", "ل", "م", "ن", "ه", "و", "ي"]
    return any(char in text for char in arabic_chars)

def update_conversation_context(session_id: str, user_message: str, category: str):
    """Track conversation context and user interests"""
    global conversation_context

    if session_id not in conversation_context:
        conversation_context[session_id] = {
            'interests': [],
            'topics': [],
            'language_preference': 'en',
            'interaction_count': 0,
            'project_interest': False,
            'technical_level': 'beginner'
        }

    context = conversation_context[session_id]
    context['interaction_count'] += 1

    # Detect language preference
    if detect_arabic(user_message):
        context['language_preference'] = 'ar'

    # Track topics and interests
    msg_lower = user_message.lower()

    # Technical interests
    tech_keywords = ['programming', 'coding', 'development', 'ai', 'machine learning', 'python', 'javascript', 'react', 'flutter']
    if any(keyword in msg_lower for keyword in tech_keywords):
        if 'technical' not in context['interests']:
            context['interests'].append('technical')
        context['technical_level'] = 'intermediate' if context['interaction_count'] > 3 else 'beginner'

    # Business/project interests
    business_keywords = ['business', 'startup', 'company', 'project', 'idea', 'build', 'develop', 'create']
    if any(keyword in msg_lower for keyword in business_keywords):
        context['project_interest'] = True
        if 'business' not in context['interests']:
            context['interests'].append('business')

    # Design interests
    design_keywords = ['design', 'ui', 'ux', 'interface', 'layout', 'visual']
    if any(keyword in msg_lower for keyword in design_keywords):
        if 'design' not in context['interests']:
            context['interests'].append('design')

    # Track conversation topics
    if category not in context['topics']:
        context['topics'].append(category)

    # Keep only recent topics (last 5)
    if len(context['topics']) > 5:
        context['topics'] = context['topics'][-5:]

def get_contextual_response_enhancement(session_id: str, base_response: str) -> str:
    """Enhance response based on conversation context"""
    if session_id not in conversation_context:
        return base_response

    context = conversation_context[session_id]

    # Add personalized touches based on context
    enhancements = []

    # If user has shown project interest
    if context['project_interest'] and context['interaction_count'] > 2:
        if context['language_preference'] == 'ar':
            enhancements.append("\n\nبالمناسبة، لاحظت اهتمامك بالمشاريع. هل تريد مناقشة فكرة معينة؟ 💡")
        else:
            enhancements.append("\n\nBy the way, I noticed your interest in projects. Want to discuss a specific idea? 💡")

    # If user is technically inclined
    if 'technical' in context['interests'] and context['technical_level'] == 'intermediate':
        if context['language_preference'] == 'ar':
            enhancements.append("\n\nيمكنني مشاركة تفاصيل تقنية أكثر إذا كنت مهتماً! 🔧")
        else:
            enhancements.append("\n\nI can share more technical details if you're interested! 🔧")

    # If user has design interests
    if 'design' in context['interests']:
        if context['language_preference'] == 'ar':
            enhancements.append("\n\nنحن في Sofflex نهتم بالتصميم الجميل والوظيفي! 🎨")
        else:
            enhancements.append("\n\nWe at Sofflex care about beautiful and functional design! 🎨")

    # Add one random enhancement to avoid being predictable
    if enhancements and len(enhancements) > 0:
        import random
        return base_response + random.choice(enhancements)

    return base_response

async def get_llama_response(user_message: str, session_id: str) -> str:
    """Get intelligent response from AI model with enhanced conversation handling"""
    global model, tokenizer, conversation_history

    # Normalize and clean user input silently
    normalized_message = normalize_input(user_message)

    if model is None or tokenizer is None:
        return get_dynamic_fallback_response(normalized_message, session_id)

    try:
        # Get or initialize conversation history
        if session_id not in conversation_history:
            conversation_history[session_id] = []

        # Create clean message format for AI model
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add recent conversation history (last 6 messages to stay within token limits)
        recent_messages = conversation_history[session_id][-6:] if len(conversation_history[session_id]) > 6 else conversation_history[session_id]
        messages.extend(recent_messages)

        # Add current user message (use normalized version)
        messages.append({"role": "user", "content": normalized_message})

        # Format conversation for the model
        if current_model_name and "dialogpt" in current_model_name.lower():
            # DialoGPT format
            conversation_text = f"FS Assistant: I'm FS from Flexible Soft, ready to help with any questions!<|endoftext|>"
            
            for msg in messages[-4:]:  # Last 4 messages for context
                if msg["role"] == "user":
                    conversation_text += f"User: {msg['content']}<|endoftext|>"
                elif msg["role"] == "assistant":
                    conversation_text += f"FS Assistant: {msg['content']}<|endoftext|>"
            
            conversation_text += f"User: {user_message}<|endoftext|>FS Assistant:"
            
            inputs = tokenizer.encode(conversation_text, return_tensors="pt")
            if torch.cuda.is_available():
                inputs = inputs.to("cuda")

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        inputs,
                        max_new_tokens=200,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            response = tokenizer.decode(outputs[0][inputs.shape[-1]:], skip_special_tokens=True)
            response = response.replace("<|endoftext|>", "").strip()

        else:
            # LLaMA format
            formatted_conversation = format_llama_conversation(messages)
            
            inputs = tokenizer(
                formatted_conversation,
                return_tensors="pt",
                truncation=True,
                max_length=4000,
                padding=True
            )

            if torch.cuda.is_available():
                inputs = {k: v.to("cuda") for k, v in inputs.items()}

            with model_lock:
                with torch.no_grad():
                    outputs = model.generate(
                        **inputs,
                        max_new_tokens=300,
                        temperature=0.7,
                        do_sample=True,
                        top_p=0.9,
                        repetition_penalty=1.1,
                        pad_token_id=tokenizer.eos_token_id,
                        eos_token_id=tokenizer.eos_token_id
                    )

            full_response = tokenizer.decode(outputs[0], skip_special_tokens=True)
            response = full_response.split("Assistant:")[-1].strip()

        # Clean and validate response
        if response and len(response) > 3 and response != user_message and not response.startswith("You are FS"):
            # Update conversation history
            conversation_history[session_id].append({"role": "user", "content": user_message})
            conversation_history[session_id].append({"role": "assistant", "content": response})

            # Limit conversation history
            if len(conversation_history[session_id]) > 20:
                conversation_history[session_id] = conversation_history[session_id][-20:]

            return response
        else:
            # Fallback to dynamic response if AI output is poor
            print(f"⚠️ AI response validation failed: '{response}' - using dynamic fallback")
            return get_dynamic_fallback_response(normalized_message, session_id)

    except Exception as e:
        print(f"❌ Error generating AI response: {e}")
        return get_dynamic_fallback_response(normalized_message, session_id)

def format_llama_conversation(messages: List[Dict]) -> str:
    """Format conversation for LLaMA models"""
    conversation = f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n{SYSTEM_PROMPT}<|eot_id|>\n"

    for msg in messages[1:]:  # Skip system message as it's already added
        role = msg["role"]
        content = msg["content"]
        conversation += f"<|start_header_id|>{role}<|end_header_id|>\n{content}<|eot_id|>\n"

    conversation += "<|start_header_id|>assistant<|end_header_id|>\n"
    return conversation

def get_dynamic_fallback_response(user_message: str, session_id: str = None) -> str:
    """Dynamic intelligent response based on question categorization when AI model is unavailable"""
    is_arabic = detect_arabic(user_message)
    category = categorize_question(user_message)

    # Update conversation context if session_id provided
    if session_id:
        update_conversation_context(session_id, user_message, category)

    # Get base response
    if category == "company":
        base_response = get_company_response(is_arabic)
    elif category == "project":
        base_response = get_project_response(user_message, is_arabic)
    elif category == "technical":
        base_response = get_technical_response(user_message, is_arabic)
    elif category == "design":
        base_response = get_design_response(user_message, is_arabic)
    elif category == "greeting":
        base_response = get_greeting_response(is_arabic)
    elif category == "general_question":
        base_response = get_general_question_response(user_message, is_arabic)
    else:
        base_response = get_general_response(user_message, is_arabic)

    # Enhance response with contextual intelligence
    if session_id:
        return get_contextual_response_enhancement(session_id, base_response)

    return base_response

def get_company_response(is_arabic: bool) -> str:
    """Response for company-specific questions with Grok-like personality"""
    if is_arabic:
        return """🚀 أهلاً! أنا FS من Sofflex

نحن لسنا مجرد شركة تقنية عادية — نحن فريق من المبدعين الذين يحولون الأفكار إلى واقع رقمي!

**ما نفعله بشغف:**
• مواقع ذكية تجذب العملاء (Next.js, React)
• تطبيقات جوال تحل مشاكل حقيقية (Flutter)
• ذكاء اصطناعي يفهم احتياجاتك (مثلي تماماً! 😉)
• متاجر إلكترونية تزيد مبيعاتك
• حلول سحابية تنمو مع أعمالك

نخدم السوق السعودي والخليجي بفهم عميق للثقافة المحلية.

عندك فكرة؟ دعنا نحولها لمشروع ناجح! 💡

📧 <EMAIL> | 🌐 sofflex-website.dev.flexible-soft.com"""
    else:
        return """🚀 Hey there! I'm FS from Sofflex

We're not just another tech company — we're digital architects who turn wild ideas into reality!

**What we craft with passion:**
• Websites that actually convert visitors to customers (Next.js, React)
• Mobile apps that solve real problems (Flutter)
• AI that understands your business (like me! 😉)
• E-commerce platforms that boost sales
• Cloud solutions that scale with your dreams

We serve Saudi Arabia & the Gulf with deep local market understanding.

Got an idea brewing? Let's turn it into your next success story! 💡

📧 <EMAIL> | 🌐 sofflex-website.dev.flexible-soft.com"""

def get_greeting_response(is_arabic: bool) -> str:
    """Response for casual greetings with personality"""
    if is_arabic:
        return """👋 أهلاً! سعيد بلقائك

أنا FS — مساعدك الذكي من Sofflex. أحب الدردشة حول أي شيء:
• تقنيات البرمجة والذكاء الاصطناعي 🤖
• أفكار مشاريع رقمية مبتكرة 💡
• حلول تقنية لأعمالك 🚀
• حتى لو كان سؤالك عن الطبخ أو السفر! 😄

إيش اللي يشغل بالك اليوم؟

📧 <EMAIL>"""
    else:
        return """👋 Hey! Great to meet you

I'm FS — your smart buddy from Sofflex. I love chatting about anything:
• Programming and AI tech 🤖
• Innovative digital project ideas 💡
• Tech solutions for your business 🚀
• Even if you want to talk about cooking or travel! 😄

What's on your mind today?

📧 <EMAIL>"""

def get_technical_response(user_message: str, is_arabic: bool) -> str:
    """Response for technical questions"""
    if is_arabic:
        return f"""🤖 سؤال تقني ممتاز!

في Flexible Soft، نتخصص في التقنيات الحديثة مثل:
• الذكاء الاصطناعي والتعلم الآلي
• تطوير الويب (Next.js, React, Python)
• تطبيقات الجوال (Flutter)
• الحوسبة السحابية والأتمتة

هل تريد مناقشة مشروع تقني محدد؟ 💻

📧 <EMAIL>"""
    else:
        return f"""🤖 Excellent technical question!

At Flexible Soft, we specialize in modern technologies like:
• AI & Machine Learning
• Web development (Next.js, React, Python)
• Mobile apps (Flutter)
• Cloud computing & automation

Want to discuss a specific tech project? 💻

📧 <EMAIL>"""

def get_project_response(user_message: str, is_arabic: bool) -> str:
    """Response for project-related questions"""
    if is_arabic:
        return f"""💼 رائع! دعنا نناقش مشروعك

يمكننا مساعدتك في تطوير:
• مواقع إلكترونية احترافية
• تطبيقات جوال للعملاء
• أنظمة إدارة الأعمال
• منصات التجارة الإلكترونية
• حلول الذكاء الاصطناعي

استشارة مجانية لمناقشة فكرتك! 🚀

📧 <EMAIL>"""
    else:
        return f"""💼 Great! Let's discuss your project

We can help you develop:
• Professional websites
• Customer mobile apps
• Business management systems
• E-commerce platforms
• AI-powered solutions

Free consultation to discuss your idea! 🚀

📧 <EMAIL>"""

def get_design_response(user_message: str, is_arabic: bool) -> str:
    """Response for design-related questions"""
    if is_arabic:
        return f"""🎨 سؤال رائع عن التصميم!

في Flexible Soft، نؤمن أن التصميم الجيد هو أساس كل منتج رقمي ناجح:
• تصميم واجهات المستخدم (UI/UX) الحديثة
• تجربة مستخدم سلسة ومتجاوبة
• هوية بصرية قوية ومتسقة
• تصميم متجاوب لجميع الأجهزة

هل تحتاج مساعدة في تصميم مشروعك؟ 🎯

📧 <EMAIL>"""
    else:
        return f"""🎨 Great design question!

At Flexible Soft, we believe good design is the foundation of every successful digital product:
• Modern UI/UX design
• Seamless and responsive user experience
• Strong and consistent visual identity
• Responsive design for all devices

Need help designing your project? 🎯

📧 <EMAIL>"""

def get_general_question_response(user_message: str, is_arabic: bool) -> str:
    """Response for general how-to and informational questions"""
    if is_arabic:
        return f"""🤔 سؤال ممتاز!

أحب الفضول والرغبة في التعلم! في Flexible Soft، نؤمن أن المعرفة هي أساس الابتكار.

سواء كان سؤالك تقنياً أو عاماً، يمكنني مساعدتك. وإذا كان لديك فكرة مشروع، يمكننا تحويلها إلى واقع رقمي!

ما الذي تريد معرفة المزيد عنه؟ 💡

📧 <EMAIL>"""
    else:
        return f"""🤔 Excellent question!

I love curiosity and the desire to learn! At Flexible Soft, we believe knowledge is the foundation of innovation.

Whether your question is technical or general, I can help you. And if you have a project idea, we can turn it into digital reality!

What would you like to know more about? 💡

📧 <EMAIL>"""

def get_general_response(user_message: str, is_arabic: bool) -> str:
    """Response for general questions with creative connections to Sofflex"""
    # Enhanced creative connections based on keywords
    msg_lower = user_message.lower()

    # Food & Cooking
    if any(word in msg_lower for word in ['cook', 'recipe', 'food', 'kitchen', 'chef', 'restaurant', 'meal']):
        if is_arabic:
            return """👨‍🍳 الطبخ فن حقيقي!

تماماً مثل الطبخ، تطوير البرمجيات يحتاج:
• مكونات طازجة (كود نظيف)
• وصفة مجربة (منهجية Agile)
• طاهٍ ماهر (فريق Sofflex)
• حب للتفاصيل (اختبارات شاملة)

عندك فكرة مشروع تحتاج "طبخها"؟ 🍳

📧 <EMAIL>"""
        else:
            return """👨‍🍳 Cooking is pure art!

Just like cooking, software development needs:
• Fresh ingredients (clean code)
• Proven recipe (Agile methodology)
• Skilled chef (Sofflex team)
• Love for details (comprehensive testing)

Got a project idea that needs "cooking"? 🍳

📧 <EMAIL>"""

    # Travel & Adventure
    elif any(word in msg_lower for word in ['travel', 'journey', 'trip', 'vacation', 'adventure', 'explore']):
        if is_arabic:
            return """✈️ السفر يفتح عوالم جديدة!

رحلة التحول الرقمي مثل السفر تماماً:
• خريطة واضحة (استراتيجية المشروع)
• دليل محلي خبير (فريق Sofflex)
• حقيبة مناسبة (التقنيات الصحيحة)
• ذكريات جميلة (نجاح مضمون)

مستعد لرحلة رقمية مثيرة؟ 🗺️

📧 <EMAIL>"""
        else:
            return """✈️ Travel opens new worlds!

Digital transformation is just like traveling:
• Clear map (project strategy)
• Expert local guide (Sofflex team)
• Right luggage (proper tech stack)
• Beautiful memories (guaranteed success)

Ready for an exciting digital adventure? 🗺️

📧 <EMAIL>"""

    # Sports & Fitness
    elif any(word in msg_lower for word in ['sport', 'fitness', 'gym', 'exercise', 'workout', 'football', 'soccer']):
        if is_arabic:
            return """⚽ الرياضة تعلمنا الانضباط!

تطوير التطبيقات مثل التدريب الرياضي:
• هدف واضح (متطلبات المشروع)
• تدريب منتظم (تطوير مستمر)
• فريق متناغم (فريق Sofflex)
• نتائج مذهلة (تطبيق ناجح)

جاهز لتدريب مشروعك الرقمي؟ 💪

📧 <EMAIL>"""
        else:
            return """⚽ Sports teach us discipline!

App development is like athletic training:
• Clear goal (project requirements)
• Regular practice (continuous development)
• Team harmony (Sofflex team)
• Amazing results (successful app)

Ready to train your digital project? 💪

📧 <EMAIL>"""

    # Music & Arts
    elif any(word in msg_lower for word in ['music', 'song', 'art', 'paint', 'draw', 'creative', 'artist']):
        if is_arabic:
            return """🎵 الفن يلهم الروح!

البرمجة فن حقيقي مثل الموسيقى:
• لحن جميل (كود أنيق)
• إيقاع متناسق (أداء سلس)
• عازف ماهر (مطور Sofflex)
• جمهور معجب (مستخدمون سعداء)

عندك سيمفونية رقمية تريد تأليفها؟ 🎼

📧 <EMAIL>"""
        else:
            return """🎵 Art inspires the soul!

Programming is true art like music:
• Beautiful melody (elegant code)
• Harmonious rhythm (smooth performance)
• Skilled musician (Sofflex developer)
• Appreciative audience (happy users)

Got a digital symphony you want to compose? 🎼

📧 <EMAIL>"""

    # Weather & Nature
    elif any(word in msg_lower for word in ['weather', 'rain', 'sun', 'nature', 'tree', 'flower', 'garden']):
        if is_arabic:
            return """🌱 الطبيعة معلم عظيم!

مثل الحديقة، المشاريع الرقمية تحتاج:
• بذور جيدة (فكرة قوية)
• تربة خصبة (تقنيات حديثة)
• رعاية مستمرة (صيانة وتطوير)
• صبر وحكمة (فريق Sofflex)

جاهز لزراعة مشروعك الرقمي؟ 🌳

📧 <EMAIL>"""
        else:
            return """🌱 Nature is a great teacher!

Like a garden, digital projects need:
• Good seeds (strong idea)
• Fertile soil (modern tech)
• Continuous care (maintenance & updates)
• Patience & wisdom (Sofflex team)

Ready to plant your digital project? 🌳

📧 <EMAIL>"""

    else:
        # Default creative response
        if is_arabic:
            return """💡 أحب الفضول!

كل سؤال يفتح باب للإبداع. في Sofflex، نؤمن أن التكنولوجيا يجب أن تحل مشاكل حقيقية وتجعل الحياة أسهل.

سواء كان سؤالك عن أي موضوع، أنا هنا لمساعدتك. وإذا خطرت لك فكرة مشروع، دعنا نحولها لواقع!

إيش اللي يشغل بالك؟ 🚀

📧 <EMAIL>"""
        else:
            return """💡 I love curiosity!

Every question opens a door to creativity. At Sofflex, we believe technology should solve real problems and make life easier.

Whatever your question is about, I'm here to help. And if you get a project idea, let's make it reality!

What's brewing in your mind? 🚀

📧 <EMAIL>"""

# FastAPI Application Routes

@app.on_event("startup")
async def startup_event():
    """Load LLaMA model on startup"""
    print("🚀 Starting FS Assistant with enhanced LLaMA integration...")
    success = load_llama_model()
    if success:
        print("✅ LLaMA model loaded successfully!")
    else:
        print("⚠️ Running in dynamic fallback mode - install transformers and get LLaMA access for full AI features")

@app.get("/")
async def root():
    return {
        "message": "FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)",
        "status": "running",
        "model_loaded": model is not None,
        "version": "6.0.0"
    }

@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "model_status": "loaded" if model is not None else "dynamic_fallback_mode",
        "conversation_sessions": len(conversation_history)
    }

@app.post("/api/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Enhanced LLaMA-powered intelligent conversation endpoint with dynamic reasoning"""

    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())

    # Get intelligent response from LLaMA or dynamic fallback
    response_message = await get_llama_response(request.message, session_id)

    return ChatResponse(
        message=response_message,
        session_id=session_id,
        timestamp=datetime.now(),
        sources=["FS - Flexible Soft AI Assistant (Enhanced LLaMA-Powered)"]
    )

@app.post("/api/chat/stream")
async def chat_stream(request: ChatRequest):
    """Streaming version of the chat endpoint for real-time responses"""

    # Generate session ID if not provided
    session_id = request.session_id or str(uuid.uuid4())

    async def generate_stream():
        """Generate streaming response"""
        try:
            # For streaming, we'll simulate token-by-token generation
            response_message = await get_llama_response(request.message, session_id)

            # Split response into words for streaming effect
            words = response_message.split()

            # Send initial metadata
            yield f"data: {json.dumps({'type': 'start', 'session_id': session_id, 'timestamp': datetime.now().isoformat()})}\n\n"

            # Stream words with small delays
            current_text = ""
            for i, word in enumerate(words):
                current_text += word + " "
                chunk_data = {
                    'type': 'chunk',
                    'content': word + " ",
                    'full_content': current_text.strip(),
                    'is_final': i == len(words) - 1
                }
                yield f"data: {json.dumps(chunk_data)}\n\n"

                # Small delay for streaming effect
                await asyncio.sleep(0.05)

            # Send completion signal
            yield f"data: {json.dumps({'type': 'end', 'final_content': current_text.strip()})}\n\n"

        except Exception as e:
            error_data = {
                'type': 'error',
                'error': str(e),
                'fallback_response': get_dynamic_fallback_response(request.message, session_id)
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

if __name__ == "__main__":
    uvicorn.run(
        "app.main_llama:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=False  # Disable reload to prevent model reloading
    )
